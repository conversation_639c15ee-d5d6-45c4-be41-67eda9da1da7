import Image from "next/image";

export default function Profile() {
  return (
    <div className="font-sans min-h-screen bg-background">
      {/* 导航栏 */}
      <nav className="flex items-center justify-center p-4 border-b border-black/[.08] dark:border-white/[.145]">
        <div className="flex items-center space-x-6 text-sm text-foreground/60">
          <a href="#" className="hover:text-foreground transition-colors">
            🏠
          </a>
          <a href="#" className="hover:text-foreground transition-colors">
            📝
          </a>
          <a href="#" className="hover:text-foreground transition-colors">
            📊
          </a>
          <a href="#" className="hover:text-foreground transition-colors">
            📧
          </a>
          <a href="#" className="hover:text-foreground transition-colors">
            👤
          </a>
        </div>
      </nav>

      {/* 主要内容区域 */}
      <main className="flex flex-col items-center justify-center px-8 py-16">
        <div className="w-full max-w-md text-center">
          {/* 头像 */}
          <div className="mb-6">
            <div className="w-24 h-24 mx-auto mb-4 rounded-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center text-white text-2xl font-bold shadow-lg">
              H
            </div>
          </div>

          {/* 用户名 */}
          <h1 className="text-2xl font-semibold text-foreground mb-2">
            Hamster1963
          </h1>

          {/* 个人简介 */}
          <p className="text-sm text-foreground/60 mb-1">
            学生党，技术爱好者
          </p>

          {/* 个人描述 */}
          <div className="text-sm text-foreground/80 mb-8 leading-relaxed">
            <p className="mb-2">
              👋 大家好，我是Hamster。
            </p>
            <p className="mb-2">
              喜欢写代码，也喜欢写文章。
            </p>
            <p>
              我会在这里分享一些技术相关的内容，一起进步。
            </p>
          </div>

          {/* 最新文章卡片 */}
          <div className="bg-white dark:bg-black/5 rounded-lg border border-black/[.08] dark:border-white/[.145] p-4 mb-8 text-left shadow-sm">
            <div className="flex items-start space-x-3">
              <div className="w-12 h-12 rounded-lg bg-gradient-to-br from-orange-400 to-red-500 flex items-center justify-center text-white text-sm font-bold flex-shrink-0">
                学习
              </div>
              <div className="flex-1 min-w-0">
                <h3 className="text-sm font-medium text-foreground mb-1">
                  学习
                </h3>
                <p className="text-xs text-foreground/60 mb-2">
                  上次更新时间：2小时前
                </p>
                <div className="flex items-center justify-between">
                  <span className="text-xs text-foreground/40">
                    Hot 🔥
                  </span>
                  <span className="text-xs text-foreground/40">
                    阅读量: 1.2k
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* 社交链接 */}
          <div className="flex items-center justify-center space-x-6 mb-8">
            <a 
              href="https://github.com" 
              target="_blank" 
              rel="noopener noreferrer"
              className="flex items-center space-x-2 text-sm text-foreground/60 hover:text-foreground transition-colors"
            >
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
              </svg>
              <span>GitHub</span>
            </a>
            
            <span className="text-foreground/20">•</span>
            
            <a 
              href="#" 
              className="flex items-center space-x-2 text-sm text-foreground/60 hover:text-foreground transition-colors"
            >
              <span>📝</span>
              <span>博客</span>
            </a>
            
            <span className="text-foreground/20">•</span>
            
            <a 
              href="#" 
              className="flex items-center space-x-2 text-sm text-foreground/60 hover:text-foreground transition-colors"
            >
              <span>📧</span>
              <span>邮箱联系</span>
            </a>
          </div>

          {/* 统计信息 */}
          <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4 mb-8">
            <div className="flex items-center justify-center space-x-2 text-sm">
              <span className="text-green-600 dark:text-green-400">✓</span>
              <span className="text-green-700 dark:text-green-300 font-medium">
                11个项目正在进行中
              </span>
            </div>
            <div className="mt-2 text-xs text-green-600 dark:text-green-400 text-center">
              <a href="#" className="hover:underline">
                查看详情
              </a>
              <span className="mx-2">|</span>
              <a href="#" className="hover:underline">
                贡献统计
              </a>
            </div>
          </div>

          {/* 底部版权信息 */}
          <div className="text-xs text-foreground/40 text-center">
            <p>© 2024 Hamster1963. 保留所有权利。</p>
            <p className="mt-1">
              基于 Next.js 构建 • 
              <a href="/login" className="hover:text-foreground transition-colors ml-1">
                管理后台
              </a>
            </p>
          </div>
        </div>
      </main>
    </div>
  );
}
